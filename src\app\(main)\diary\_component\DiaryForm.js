'use client';

import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { setSubject, setMessage } from '@/store/features/diarySlice';
import { formatDate } from '@/utils/dateFormatter';
import FeedbackViewModal from './FeedbackViewModal';
import Image from 'next/image';


const DiaryForm = ({
  today,
  todayEntry,
  subject,
  message,
  wordCount,
  selectedStage,
}) => {
  const dispatch = useDispatch();
  const isDisabled = todayEntry?.hasGreeting === false;
  const CorrectionData = todayEntry?.correction;
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  // console.log(todayEntry, 'corection data');
  // Function to handle message changes with word limit validation
  const handleMessageChange = (e) => {
    // If hasGreeting is false, open the modal and prevent input
    if (todayEntry?.hasGreeting === false) {
      // We need to set isMessageModalOpen to true in the parent component
      // onClose is actually closing the modal, not opening it
      window.dispatchEvent(new CustomEvent('openGreetingModal'));
      return;
    }

    const newText = e.target.value;
    dispatch(setMessage(newText));
  };

  return (
    <>
      <div className="flex flex-col h-full">
        <div className={`${CorrectionData ? 'h-1/2' : 'h-full'} pb-2`}>
          <div className="bg-white rounded-xl shadow-md border-2 p-2 flex flex-col h-full">
            {todayEntry?.hasGreeting === false && (
              <div className="mb-4 p-3 rounded-md text-sm bg-[#FFD3D8]">
                <p className={isDisabled ? 'text-red-800' : 'text-yellow-800'}>
                  <span className="font-semibold">Note:</span> Please send a
                  greeting message to your tutor before writing your diary
                  entry.
                </p>
              </div>
            )}

            <div className="flex justify-between items-baseline pb-2 border-b-2 border-dotted border-gray-300 mb-4">
              <input
                type="text"
                placeholder="Write subject here..."
                value={subject}
                onChange={(e) => {
                  if (todayEntry?.hasGreeting === false) {
                    window.dispatchEvent(new CustomEvent('openGreetingModal'));
                    return;
                  }
                  dispatch(setSubject(e.target.value));
                }}
                className="flex-grow p-0 border-none outline-none text-lg font-medium bg-transparent"
              />
              <span className="text-sm text-gray-500 whitespace-nowrap">
                {' '}
                {today}
              </span>
            </div>

            <div className="relative flex-grow">
              <textarea
                placeholder="Write here..."
                value={message}
                onChange={handleMessageChange}
                className="w-full h-full p-3 border rounded-md border-gray-300"
              />
              <div className="text-right text-sm mt-1 text-gray-500">
                {wordCount} / {selectedStage?.wordLimit || 50} words
              </div>
            </div>
          </div>
        </div>
        <div
          className={`${
            CorrectionData || todayEntry?.feedbacks?.length > 0
              ? 'h-1/2'
              : 'h-0'
          }  p-3 rounded-md bg-white shadow-xl overflow-auto relative mt-2`}
        >
          {CorrectionData ? (
            <div className="h-full">
              <div className="h-full">
                <p className="text-sm text-[#864D0D] text-center font-medium mb-2">
                  Tutor Review Zone
                </p>
                <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                  <h3 className="text-lg font-semibold mb-2">
                    {todayEntry?.title}
                  </h3>
                  <div className="flex items-center gap-3 text-sm">
                    {formatDate(todayEntry.entryDate, 'ordinal')}
                  </div>
                </div>
                <span
                  dangerouslySetInnerHTML={{
                    __html: CorrectionData.correctionText || '',
                  }}
                />
                {todayEntry?.status === 'confirm' && (
                   <div className="text-center p-5">
                          <h1 className="text-lg text-[#14AE5C] font-medium">
                            Confirmed
                          </h1>
                        </div>
                )}
              </div>
            </div>
          ) : todayEntry?.feedbacks?.length > 0 ? (
            <div className="h-full flex items-center justify-center">
              <p className="text-gray-500 italic">No review available yet.</p>
            </div>
          ) : null}

          {todayEntry?.feedbacks?.length > 0 && (
            <div className="absolute right-0 bottom-0">
              <button
                className=""
                onClick={() => setShowFeedbackModal(true)}
                aria-label="View feedback"
              >
                <Image
                  src="/assets/images/all-img/feedback-bg.png"
                  alt="Feedback"
                  width={50}
                  height={50}
                  className="w-full h-full object-contain"
                />
              </button>
            </div>
          )}

          {/* Feedback Modal */}
          {showFeedbackModal && (
            <FeedbackViewModal
              isOpen={showFeedbackModal}
              onClose={() => setShowFeedbackModal(false)}
              feedbacks={todayEntry?.feedbacks || []}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default DiaryForm;
